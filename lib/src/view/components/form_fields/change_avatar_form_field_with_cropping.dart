import 'dart:io';
import 'package:almashal/src/core/utils/platform_helper.dart';
import 'package:almashal/src/view/components/bottomsheets/custom_bottom_sheet.dart';
import 'package:almashal/src/view/components/image/custom_cached_network_image.dart';
import 'package:almashal/src/controllers/profile_controller.dart';
import 'package:feather_icons/feather_icons.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

class ChangeAvatarFormFieldWithCropping extends StatefulWidget {
  const ChangeAvatarFormFieldWithCropping({
    super.key,
    this.imageUrl,
    this.onImageUpdated,
    this.onChanged,
    this.enabled = true,
  });

  final String? imageUrl;
  final VoidCallback? onImageUpdated;
  final Function(XFile?)? onChanged;
  final bool enabled;

  @override
  State<ChangeAvatarFormFieldWithCropping> createState() =>
      _ChangeAvatarFormFieldWithCroppingState();
}

class _ChangeAvatarFormFieldWithCroppingState
    extends State<ChangeAvatarFormFieldWithCropping> {
  final ProfileController _profileController = Get.find<ProfileController>();
  final RxBool _isUpdating = false.obs;
  XFile? _selectedImage;

  @override
  Widget build(BuildContext context) {
    return Obx(() => Stack(
          children: [
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: _buildAvatarContent(),
            ),
            if (widget.enabled)
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(20),
                      onTap: _isUpdating.value
                          ? null
                          : _showImagePickerBottomSheet,
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: _isUpdating.value
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                ),
                              )
                            : const Icon(
                                FeatherIcons.edit,
                                size: 20,
                                color: Colors.white,
                              ),
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ));
  }

  Widget _buildAvatarContent() {
    if (_selectedImage != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(100),
        child: Image.file(
          File(_selectedImage!.path),
          fit: BoxFit.cover,
          width: 100,
          height: 100,
        ),
      );
    } else if (widget.imageUrl != null && widget.imageUrl!.isNotEmpty) {
      return CustomCachedNetworkImage(
        imageUrl: widget.imageUrl!,
        borderRadius: BorderRadius.circular(100),
        width: 100,
        height: 100,
        fit: BoxFit.cover,
      );
    } else {
      return CircleAvatar(
        backgroundColor: Theme.of(context).primaryColor,
        child: const Icon(
          Icons.person,
          color: Colors.white,
          size: 50,
        ),
      );
    }
  }

  void _showImagePickerBottomSheet() {
    Get.bottomSheet(
      CustomBottomSheet(
        title: 'تغيير الصورة الشخصية',
        body: Column(
          children: [
            ListTile(
              title: const Text('الكاميرا'),
              subtitle: const Text('التقاط صورة جديدة'),
              leading: const Icon(FeatherIcons.camera),
              onTap: () => _pickImage(ImageSource.camera),
            ),
            ListTile(
              title: const Text('المعرض'),
              subtitle: const Text('اختيار من الصور المحفوظة'),
              leading: const Icon(FeatherIcons.image),
              onTap: () => _pickImage(ImageSource.gallery),
            ),
            const Divider(),
            ListTile(
              title: const Text('معلومات'),
              subtitle: const Text('سيتم قص الصورة تلقائياً لتصبح مربعة الشكل'),
              leading: Icon(
                FeatherIcons.info,
                color: Theme.of(context).primaryColor,
              ),
              enabled: false,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      Get.back(); // Close bottom sheet

      final ImagePicker picker = ImagePicker();
      XFile? image;

      if (PlatformHelper.isWeb && source == ImageSource.camera) {
        // For web, use gallery instead of camera as camera might not be available
        image = await picker.pickImage(
          source: ImageSource.gallery,
          imageQuality: 85,
        );
      } else {
        image = await picker.pickImage(
          source: source,
          imageQuality: 85,
        );
      }

      if (image != null) {
        _selectedImage = image;
        await _processAndUploadImage(image);
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء اختيار الصورة: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _processAndUploadImage(XFile imageFile) async {
    _isUpdating.value = true;

    try {
      // Use ProfileController's cropping functionality
      final file =
          await _profileController.updateProfileImageWithCropping(imageFile);

      if (file != null) {
        // Notify parent widget that image was updated
        widget.onImageUpdated?.call();
        widget.onChanged?.call(XFile.fromData(file.readAsBytesSync()));
        // Show success message (already handled in ProfileController)
        // Additional UI updates can be done here if needed
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث الصورة: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      _isUpdating.value = false;
    }
  }

  /// Method to manually trigger image update (can be called from parent)
  Future<void> updateImageFromFile(File imageFile) async {
    _isUpdating.value = true;

    try {
      final xFile = XFile(imageFile.path);
      final file =
          await _profileController.updateProfileImageWithCropping(xFile);
      if (file != null) {
        final xFile = XFile.fromData(file.readAsBytesSync());
        setState(() {
          _selectedImage = xFile;
        });
        widget.onChanged?.call(xFile);
      }
      // Handle successful update (if needed)
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث الصورة: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      _isUpdating.value = false;
    }
  }

  /// Get the currently selected image file
  XFile? get selectedImage => _selectedImage;

  /// Check if an update is currently in progress
  bool get isUpdating => _isUpdating.value;
}
