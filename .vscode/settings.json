{
  "cSpell.words": [
    "إدخال"
  ],
  // Flutter Debug Configuration
  "dart.debugExternalPackageLibraries": true,
  "dart.debugSdkLibraries": true,
  "dart.showInspectorNotificationsForWidgetErrors": true,
  "dart.previewFlutterUiGuides": true,
  "dart.previewFlutterUiGuidesCustomTracking": true,
  "dart.flutterHotReloadOnSave": "always",
  "dart.flutterHotRestartOnSave": "never",
  "dart.openDevTools": "flutter",
  "dart.devToolsTheme": "dark",
  "dart.showMainCodeLens": true,
  "dart.showTestCodeLens": true,
  "dart.analysisServerFolding": true,
  "dart.closingLabels": true,
  "dart.enableCompletionCommitCharacters": true,
  "dart.enableSnippets": true,
  "dart.insertArgumentPlaceholders": true,
  "dart.lineLength": 120,
  "dart.runPubGetOnPubspecChanges": "prompt",
  "dart.warnWhenEditingFilesOutsideWorkspace": true,
  "dart.allowAnalytics": false,
  "dart.checkForSdkUpdates": true,
  "dart.flutterCreateAndroidLanguage": "kotlin",
  "dart.flutterCreateIOSLanguage": "swift",
  "dart.flutterCreateOrganization": "com.almashalfamily",
  "dart.maxLogLineLength": 2000,
  "dart.showSkippedTests": true,
  "dart.vmServiceLogFile": "dart_vm_service.log",
  
  // Console and Debug Output Settings
  "debug.console.fontSize": 14,
  "debug.console.fontFamily": "Monaco, 'Courier New', monospace",
  "debug.console.lineHeight": 1.4,
  "debug.console.wordWrap": true,
  "debug.internalConsoleOptions": "openOnSessionStart",
  "debug.openDebug": "openOnSessionStart",
  "debug.showBreakpointsInOverviewRuler": true,
  "debug.showInlineBreakpointCandidates": true,
  
  // Terminal Settings for Flutter
  "terminal.integrated.fontSize": 14,
  "terminal.integrated.fontFamily": "Monaco, 'Courier New', monospace",
  "terminal.integrated.scrollback": 10000,
  
  // Editor Settings for Better Debugging
  "editor.rulers": [80, 120],
  "editor.wordWrap": "bounded",
  "editor.wordWrapColumn": 120,
  "editor.minimap.enabled": true,
  "editor.minimap.showSlider": "always",
  "editor.bracketPairColorization.enabled": true,
  "editor.guides.bracketPairs": true,
  "editor.guides.bracketPairsHorizontal": true,
  "editor.guides.highlightActiveIndentation": true,
  "editor.guides.indentation": true,
  
  // File Association
  "files.associations": {
    "*.dart": "dart",
    "pubspec.yaml": "yaml",
    "analysis_options.yaml": "yaml"
  },
  
  // Search and File Exclusions
  "search.exclude": {
    "**/build/**": true,
    "**/.dart_tool/**": true,
    "**/ios/Pods/**": true,
    "**/android/.gradle/**": true,
    "**/android/app/build/**": true
  },
  
  // File Watcher Exclusions
  "files.watcherExclude": {
    "**/build/**": true,
    "**/.dart_tool/**": true,
    "**/ios/Pods/**": true,
    "**/android/.gradle/**": true,
    "**/android/app/build/**": true
  },
  "accessibility.signals.terminalBell": {
    "sound": "off"
  },
  "terminal.integrated.enableVisualBell": false
}
