{"version": "0.2.0", "configurations": [{"name": "Flutter Debug (Development)", "request": "launch", "type": "dart", "program": "lib/main.dart", "args": ["--flavor", "development", "--dart-define=ENVIRONMENT=development"], "console": "debugConsole", "debugExternalPackageLibraries": true, "debugSdkLibraries": true, "showMemoryUsage": true, "vmServicePort": 0, "enableAsserts": true, "additionalProjectPaths": []}, {"name": "Flutter Debug (Profile Mode)", "request": "launch", "type": "dart", "program": "lib/main.dart", "flutterMode": "profile", "console": "debugConsole", "debugExternalPackageLibraries": false, "debugSdkLibraries": false, "showMemoryUsage": true, "vmServicePort": 0}, {"name": "Flutter Debug (Release Mode)", "request": "launch", "type": "dart", "program": "lib/main.dart", "flutterMode": "release", "console": "debugConsole", "debugExternalPackageLibraries": false, "debugSdkLibraries": false, "showMemoryUsage": false, "vmServicePort": 0}, {"name": "Flutter Debug Console Test", "request": "launch", "type": "dart", "program": "lib/debug_test.dart", "console": "debugConsole", "debugExternalPackageLibraries": true, "debugSdkLibraries": true, "showMemoryUsage": true, "vmServicePort": 0, "enableAsserts": true}, {"name": "Flutter Web Debug", "request": "launch", "type": "dart", "program": "lib/main.dart", "flutterPlatform": "web", "webRenderer": "html", "console": "debugConsole", "debugExternalPackageLibraries": true, "debugSdkLibraries": true, "args": ["--web-port=3000", "--web-hostname=localhost"]}, {"name": "Flutter iOS Debug", "request": "launch", "type": "dart", "program": "lib/main.dart", "flutterPlatform": "ios", "console": "debugConsole", "debugExternalPackageLibraries": true, "debugSdkLibraries": true, "showMemoryUsage": true, "vmServicePort": 0, "enableAsserts": true}]}